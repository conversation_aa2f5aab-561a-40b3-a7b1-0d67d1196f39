<?php

namespace App\Services\WebhookService\Formatters;

use App\Services\Abstract\AbstractFormatter;

class PostbackMessageFormatter extends AbstractFormatter
{
    protected function canHandle(array $payload): bool
    {
        return isset($payload['postback']);
    }

    public function format(array $message): array
	{
		return [
			'sender'       => $message['sender']['id'],
			'receiver'     => $message['recipient']['id'],
			'message_type' => 'postback',
			'postback'     => [
				'title'   => $message['postback']['title'],
				'payload' => $message['postback']['payload'],
			],
			'timestamp'    => gmdate('Y-m-d\TH:i:s\Z', $message['timestamp'] / 1000),
			'is_admin'     => isset($message['message']['is_echo']),
		];
	}
}
