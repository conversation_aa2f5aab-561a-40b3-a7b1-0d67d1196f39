<?php

namespace App\Services\Http;

use App\Contracts\HttpClientInterface;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TelegramHttpClient implements HttpClientInterface
{
    protected string $baseUrl;
    protected int $timeout;

    public function __construct(string $botToken, int $timeout = 30)
    {
        $this->baseUrl = 'https://api.telegram.org/bot' . $botToken;
        $this->timeout = $timeout;
    }

    public function get(string $path, array $headers = [], array $query = []): Response
    {
        // Build the full URL including query string
        $url = $this->baseUrl . $path;
        if (!empty($query)) {
            $url .= '?' . http_build_query($query);
        }

        // Log it (<PERSON><PERSON>'s logger)
        Log::info('Calling external API', [
            'url' => $url,
            'headers' => $headers,
        ]);
        $response = Http::withHeaders($headers)
            ->timeout($this->timeout)
            ->get($this->baseUrl . $path, $query);
        Log::info('External API Response', [
            'status' => $response->status(),
            'body'   => $response->body(),   // full body (string)
            // 'json' => $response->json(),  // uncomment if you prefer parsed JSON
        ]);
        return $response;
    }

    public function post(string $path, array $headers, array $body = []): Response
    {
        $request = Http::withHeaders($headers)->timeout($this->timeout);

        return empty($body)
            ? $request->post($this->baseUrl . $path)
            : $request->post($this->baseUrl . $path, $body);
    }

    public function delete(string $path, array $headers = [], array $query = []): Response
    {
        return Http::withHeaders($headers)
            ->timeout($this->timeout)
            ->delete($this->baseUrl . $path, $query);
    }
}
