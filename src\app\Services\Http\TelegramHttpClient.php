<?php

namespace App\Services\Http;

use App\Contracts\HttpClientInterface;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class TelegramHttpClient implements HttpClientInterface
{
    protected string $baseUrl;
    protected int $timeout;

    public function __construct(string $botToken, int $timeout = 30)
    {
        $this->baseUrl = 'https://api.telegram.org/bot' . $botToken;
        $this->timeout = $timeout;
    }

    public function get(string $path, array $headers = [], array $query = []): Response
    {
        $url = $this->baseUrl . $path;
        if (!empty($query)) {
            $url .= '?' . http_build_query($query);
        }

        \Log::info('Calling Telegram API', [
            'url' => $url,
            'headers' => $headers,
        ]);

        $response = Http::withHeaders($headers)
            ->timeout($this->timeout)
            ->get($this->baseUrl . $path, $query);

        \Log::info('Telegram API Response', [
            'status' => $response->status(),
            'body' => $response->body(),
        ]);

        return $response;
    }

    public function post(string $path, array $headers = [], array $body = []): Response
    {
        $url = $this->baseUrl . $path;

        \Log::info('Calling Telegram API', [
            'url' => $url,
            'headers' => $headers,
            'body' => $body,
        ]);

        $request = Http::withHeaders($headers)->timeout($this->timeout);

        $response = empty($body)
            ? $request->post($url)
            : $request->post($url, $body);

        \Log::info('Telegram API Response', [
            'status' => $response->status(),
            'body' => $response->body(),
        ]);

        return $response;
    }

    public function delete(string $path, array $headers = [], array $query = []): Response
    {
        return Http::withHeaders($headers)
            ->timeout($this->timeout)
            ->delete($this->baseUrl . $path, $query);
    }
}
