<?php

namespace App\Providers;

use App\Services\WebhookService\Formatters\LiveCommentFormatter;
use App\Services\WebhookService\Formatters\PostCommentFormatter;
use App\Services\WebhookService\WebhookPreprocessor;
use Illuminate\Support\ServiceProvider;
use App\Services\WebhookService\WebhookExtractor;
use App\Services\WebhookService\FormatterChainBuilder;

// Direct Message Formatters
use App\Services\WebhookService\Formatters\StoryReplyFormatter;
use App\Services\WebhookService\Formatters\TextMessageFormatter;
use App\Services\WebhookService\Formatters\ImageMessageFormatter;
use App\Services\WebhookService\Formatters\AudioMessageFormatter;
use App\Services\WebhookService\Formatters\VideoMessageFormatter;
use App\Services\WebhookService\Formatters\ReelsMessageFormatter;
use App\Services\WebhookService\Formatters\PostShareFormatter;
use App\Services\WebhookService\Formatters\PostbackMessageFormatter;
use App\Services\WebhookService\Formatters\QuickReplyMessageFormatter;
use App\Services\WebhookService\Formatters\ReadReceiptFormatter;

class WebhookServiceProvider extends ServiceProvider
{
    public function register()
    {
        // Register all formatters as singletons
        $this->app->singleton(StoryReplyFormatter::class, fn() => new StoryReplyFormatter());
        $this->app->singleton(TextMessageFormatter::class, fn() => new TextMessageFormatter());
        $this->app->singleton(ImageMessageFormatter::class, fn() => new ImageMessageFormatter());
        $this->app->singleton(AudioMessageFormatter::class, fn() => new AudioMessageFormatter());
        $this->app->singleton(VideoMessageFormatter::class, fn() => new VideoMessageFormatter());
        $this->app->singleton(ReelsMessageFormatter::class, fn() => new ReelsMessageFormatter());
        $this->app->singleton(PostShareFormatter::class, fn() => new PostShareFormatter());
        $this->app->singleton(PostbackMessageFormatter::class, fn() => new PostbackMessageFormatter());
        $this->app->singleton(QuickReplyMessageFormatter::class, fn() => new QuickReplyMessageFormatter());
        $this->app->singleton(ReadReceiptFormatter::class, fn() => new ReadReceiptFormatter());
        $this->app->singleton(PostCommentFormatter::class, fn() => new PostCommentFormatter());
        $this->app->singleton(LiveCommentFormatter::class, fn() => new LiveCommentFormatter());

        // Register the FormatterChainBuilder
        $this->app->singleton(FormatterChainBuilder::class, fn() => new FormatterChainBuilder());

        // Register the WebhookPreprocessor
        $this->app->singleton(WebhookPreprocessor::class, fn() => new WebhookPreprocessor());

        // Register the WebhookExtractor using the chain builder
        $this->app->singleton(WebhookExtractor::class, function ($app) {
            $formatters = [
                $app->make(PostCommentFormatter::class),
                $app->make(LiveCommentFormatter::class),
                $app->make(QuickReplyMessageFormatter::class),
                $app->make(StoryReplyFormatter::class),
                $app->make(TextMessageFormatter::class),
                $app->make(ImageMessageFormatter::class),
                $app->make(AudioMessageFormatter::class),
                $app->make(VideoMessageFormatter::class),
                $app->make(ReelsMessageFormatter::class),
                $app->make(PostShareFormatter::class),
                $app->make(PostbackMessageFormatter::class),
                $app->make(ReadReceiptFormatter::class),
            ];

            $chainBuilder = $app->make(FormatterChainBuilder::class);
            $firstFormatter = $chainBuilder->build($formatters);

            $preprocessor = $app->make(WebhookPreprocessor::class);

            return new WebhookExtractor(
                preprocessor: $preprocessor,
                firstFormatter: $firstFormatter
            );
        });
    }
}
