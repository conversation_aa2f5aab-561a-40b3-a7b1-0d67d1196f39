<?php

namespace App\Services\Validators;

use App\Exceptions\InvalidApiTokenException;
use App\Models\SpecialUser;

class ApiTokenValidationService
{
    /**
     * Validates if the provided API token is correct, and then returns the related Special User.
     *
     * @param string|null $apiToken
     * @return SpecialUser
     * @throws InvalidApiTokenException
     */
    public static function validateApiToken(?string $apiToken): SpecialUser
    {
        if (!$apiToken) {
            throw new InvalidApiTokenException(apiToken: $apiToken, message: "API token is required");
        }

        $specialUser = SpecialUser::getUserByApiToken($apiToken);
        if (!$specialUser) {
            throw new InvalidApiTokenException(apiToken: $apiToken);
        }

        return $specialUser;
    }
}
