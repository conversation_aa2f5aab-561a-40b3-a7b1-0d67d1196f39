<?php

namespace App\Services\TelegramService;

/**
 * Example usage of the TelegramMessageService
 * This demonstrates how to use the service and what gets logged
 */
class TelegramServiceExample
{
    public function sendExampleMessage(): array
    {
        $telegramService = new TelegramMessageService();
        
        // Example parameters - replace with real values
        $botToken = 'YOUR_BOT_TOKEN_HERE';  // Get from @BotFather
        $userId = 'USER_CHAT_ID';           // The chat ID of the recipient
        $message = 'Hello from your Laravel application!';
        
        try {
            // This will log:
            // 1. "Calling external API" with URL and headers
            // 2. "External API Response" with status and response body
            $response = $telegramService->sendMessage($botToken, $userId, $message);
            
            return [
                'success' => true,
                'message_id' => $response['result']['message_id'] ?? null,
                'response' => $response
            ];
            
        } catch (\Exception $e) {
            // This will also be logged if there's an error
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Example of what gets logged when sending a message:
     * 
     * [INFO] Calling external API
     * {
     *   "url": "https://api.telegram.org/botYOUR_TOKEN/sendMessage",
     *   "headers": {
     *     "Content-Type": "application/json"
     *   }
     * }
     * 
     * [INFO] External API Response
     * {
     *   "status": 200,
     *   "body": "{\"ok\":true,\"result\":{\"message_id\":123,\"from\":{...},\"chat\":{...},\"date\":1234567890,\"text\":\"Hello from your Laravel application!\"}}"
     * }
     */
}
