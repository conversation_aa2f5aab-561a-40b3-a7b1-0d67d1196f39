<?php

namespace App\Services\Token;

use App\Contracts\TokenDecryptorInterface;
use phpseclib3\Crypt\AES;
use phpseclib3\Crypt\PublicKeyLoader;

class PhpseclibTokenDecryptor implements TokenDecryptorInterface
{
    private TokenParser $parser;

    public function __construct(
        TokenParser $parser
    ) {
        $this->parser = $parser;
    }

    public function decrypt(string $encryptedToken): string
    {
        try {
            [$encryptedData, $encryptedAesKey, $iv] = $this->parser->parse($encryptedToken);

            $privateKey = config('app.cyber_key');
            $password = config('app.private_key_password');

            $rsa = PublicKeyLoader::load($privateKey, $password);
            $aesKey = $rsa->decrypt($encryptedAesKey);

            $cipher = new AES('cbc');
            $cipher->setKey($aesKey);
            $cipher->setIV($iv);

            return $cipher->decrypt($encryptedData);
        } catch (\Throwable $e) {
            return $encryptedToken;
        }
    }
}
