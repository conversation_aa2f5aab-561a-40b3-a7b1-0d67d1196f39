<?php

namespace App\Http\Controllers\Messenger;

use App\Exceptions\InvalidApiTokenException;
use App\Exceptions\InvalidEndpointException;
use App\Exceptions\InvalidMessageTypeException;
use App\Http\Controllers\Abstract\AbstractMessageController;
use App\Services\MessageService\MessageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class ButtonTextMessageController extends AbstractMessageController
{
	protected MessageService $messageService;

	public function __construct(MessageService $messageService)
	{
		$this->messageService = $messageService;
	}

	/**
	 * Sends a text message.
	 *
	 * @param Request $request
	 * @return JsonResponse
	 * @throws InvalidMessageTypeException
	 * @throws InvalidEndpointException
	 * @throws InvalidApiTokenException
	 * @throws ValidationException
	 */
	public function send(Request $request): JsonResponse
	{
		$specialUser = $this->validateRequest($request);

		$validator = Validator::make($request->all(), [
            'receiver_id' => 'required_without:comment_id|string',
            'comment_id'  => 'required_without:receiver_id|string',
			'text'              => 'required|string',
			'buttons'           => 'required|array',
			'buttons.*.type'    => 'required|string|in:web_url,postback',
			'buttons.*.title'   => 'required|string',
			'buttons.*.url'     => 'nullable|url',
			'buttons.*.payload' => 'nullable|string',
		]);

		// Add custom validation logic for conditional rules
		$validator->after(function ($validator) use ($request) {
			$buttons = $request->input('buttons', []);

			foreach ($buttons as $index => $button) {
				if (isset($button['type'])) {
					if ($button['type'] === 'web_url' && empty($button['url']))
						$validator->errors()->add("buttons.$index.url", 'The URL is required when the button type is web_url.');

					if ($button['type'] === 'postback' && empty($button['payload']))
						$validator->errors()->add("buttons.$index.payload", 'The payload is required when the button type is postback.');
				}
			}
		});

		if ($validator->fails()) {
			return response()->json([
				'success' => false,
				'message' => 'Validation failed',
				'errors'  => $validator->errors(),
			], 422);
		}

		$messageId = $this->messageService->sendMessage(
			$specialUser,
			$validator->validated(),
			'button-text',
		);

		return response()->json([
			'success'    => true,
			'message'    => 'Message sent successfully',
			'message_id' => $messageId,
		]);
	}
}
