<?php

namespace App\Services\Http;

use App\Contracts\HttpClientInterface;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Log;

class RetryHttpClient implements HttpClientInterface
{
    public function __construct(
        protected HttpClientInterface $client,
        protected int $retries = 3
    ) {}

    public function post(string $path, array $headers, array $body): Response
    {
        return $this->attempt(function () use ($path, $headers, $body) {
            return $this->client->post($path, $headers, $body);
        }, "POST", $path);
    }

    public function get(string $path, array $headers = [], array $query = []): Response
    {
        return $this->attempt(function () use ($path, $headers, $query) {
            return $this->client->get($path, $headers, $query);
        }, "GET", $path);
    }

    public function delete(string $path, array $headers = [], array $query = []): Response
    {
        return $this->attempt(function () use ($path, $headers, $query) {
            return $this->client->delete($path, $headers, $query);
        }, "DELETE", $path);
    }

    protected function attempt(callable $callback, string $method, string $path): Response
    {
        $attempt = 0;
        $lastException = null;

        while ($attempt < $this->retries) {
            try {
                return $callback();
            } catch (\Throwable $e) {
                $attempt++;
                $lastException = $e;
                Log::warning("Retry #{$attempt} failed for Instagram {$method} {$path}: {$e->getMessage()}");
                usleep(100_000);
            }
        }

        throw $lastException;
    }
}
