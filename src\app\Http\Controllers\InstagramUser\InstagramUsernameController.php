<?php

namespace App\Http\Controllers\InstagramUser;

use App\Exceptions\InvalidApiTokenException;
use App\Exceptions\InvalidEndpointException;
use App\Exceptions\InvalidUserInfoTypeException;
use App\Http\Controllers\Abstract\AbstractInstagramUserController;
use App\Services\InstagramUserService\InstagramUserInfoService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class InstagramUsernameController extends AbstractInstagramUserController
{
    protected InstagramUserInfoService $userInfoService;

    public function __construct(InstagramUserInfoService $userInfoService)
    {
        $this->userInfoService = $userInfoService;
    }

    /**
     * Fetches the username of a user.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws InvalidUserInfoTypeException
     * @throws InvalidEndpointException
     * @throws InvalidApiTokenException
     * @throws ValidationException
     */
    public function fetchInfo(Request $request): JsonResponse
    {
        $specialUser = $this->validateRequest($request);

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors'  => $validator->errors(),
            ], 422);
        }

        $data = $this->userInfoService->getUserInfo(
            $specialUser,
            $validator->validated()['user_id'],
            ['username']
        );

        return response()->json([
            'success' => true,
            'data'    => [
                'username' => $data['username'] ?? null,
            ],
        ], 200);
    }
}
