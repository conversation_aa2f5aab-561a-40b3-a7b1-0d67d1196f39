<?php

namespace App\Factories\Http;

class HttpClientFactory
{
    public static function forMessages(): \App\Contracts\HttpClientInterface
    {
        return new \App\Services\Http\RetryHttpClient(
            client: new \App\Services\Http\InstagramHttpClient(
                apiVersion: '21.0',
                timeout: 10
            ),
            retries: 2
        );
    }

    public static function forUserInfo(): \App\Contracts\HttpClientInterface
    {
        return new \App\Services\Http\InstagramHttpClient(
            apiVersion: '22.0',
            timeout: 20
        );
    }

    public static function forComments(): \App\Contracts\HttpClientInterface
    {
        return new \App\Services\Http\InstagramHttpClient(
            apiVersion: '23.0',
            timeout: 10
        );
    }
}
