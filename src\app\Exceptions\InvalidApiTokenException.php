<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class InvalidApiTokenException extends Exception
{
    protected ?string $apiToken;

    /**
     * Creates a new exception instance.
     *
     * @param string|null $apiToken
     * @param string|null $message
     */
    public function __construct(?string $apiToken, $message = null)
    {
        $this->apiToken = $apiToken;

        $message = $message ?: "Invalid API token: '{$apiToken}'";

        parent::__construct($message);
    }

    /**
     * Gets the API token.
     *
     * @return string|null
     */
    public function getApiToken(): ?string
    {
        return $this->apiToken;
    }

    /**
     * Reports the exception to the logs or external services.
     *
     * @return void
     */
    public function report(): void
    {
        Log::error("Invalid API token: {$this->apiToken}", ['exception' => $this->getMessage()]);
    }

    /**
     * Renders the exception into an HTTP response.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function render(Request $request): JsonResponse
    {
        return response()->json([
            'error'       => $this->getMessage(),
            'api_token'   => $this->getApiToken(),
        ], status: 401);
    }
}
