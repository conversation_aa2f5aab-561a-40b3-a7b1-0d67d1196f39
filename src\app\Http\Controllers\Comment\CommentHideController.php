<?php

namespace App\Http\Controllers\Comment;

use App\Http\Controllers\Abstract\AbstractCommentController;
use App\Services\CommentService\CommentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CommentHideController extends AbstractCommentController
{
    protected CommentService $commentService;

    public function __construct(CommentService $commentService)
    {
        $this->commentService = $commentService;
    }

    public function hide(Request $request): JsonResponse
    {
        $specialUser = $this->validateRequest($request);

        $validator = Validator::make($request->all(), [
            'comment_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors'  => $validator->errors(),
            ], 422);
        }

        $this->commentService->handle(
            $specialUser,
            $validator->validated(),
            'hide'
        );

        return response()->json([
            'success' => true,
            'message' => 'Comment hidden successfully',
        ], 200);
    }
}
