<?php

namespace App\Repositories;

use App\Models\SpecialUser;
use App\Repositories\Interfaces\SpecialUserRepositoryInterface;
use Illuminate\Support\Facades\Cache;

class CachingSpecialUserRepository implements SpecialUserRepositoryInterface
{
    private SpecialUserRepositoryInterface $repo;

    public function __construct(
        SpecialUserRepositoryInterface $repo
    ) {
        $this->repo = $repo;
    }

    public function findByInstagramId(string $instagramId): ?SpecialUser
    {
        return Cache::remember(
            "special_user_instagram_{$instagramId}",
            now()->addMinutes(10), function () use ($instagramId) {
                return $this->repo->findByInstagramId($instagramId);
            }
        );
    }

    public function existsByInstagramId(string $instagramId): bool
    {
        return Cache::remember(
            "special_user_exists_instagram_{$instagramId}",
            now()->addMinutes(5), function () use ($instagramId) {
                return $this->repo->existsByInstagramId($instagramId);
            }
        );
    }

    public function isValidEndpoint(SpecialUser $user, string $endpoint): bool
    {
        return $this->repo->isValidEndpoint($user, $endpoint);
    }

    public function setApiToken(SpecialUser $user, string $apiToken): void
    {
        $this->repo->setApiToken($user, $apiToken);

        Cache::forget("special_user_instagram_{$user->instagram_id}");
        Cache::forget("special_user_exists_instagram_{$user->instagram_id}");
    }

    public function getUserByApiToken(string $apiToken): ?SpecialUser
    {
        // Cannot cache this since we hash-compare all rows dynamically
        return $this->repo->getUserByApiToken($apiToken);
    }

    public function decryptAccessToken(string $encryptedToken): string
    {
        return $this->repo->decryptAccessToken($encryptedToken);
    }
}
