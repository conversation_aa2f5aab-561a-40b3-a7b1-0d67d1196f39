<?php

namespace App\Services\WebhookService\Formatters;

use App\Services\Abstract\AbstractFormatter;

class ReadReceiptFormatter extends AbstractFormatter
{
    protected function canHandle(array $payload): bool
    {
        return isset($payload['read']);
    }

    public function format(array $message): array
    {
        return [
            'sender'       => $message['sender']['id'],
            'receiver'     => $message['recipient']['id'],
            'message_type' => 'read',
            'seen' => [
                'message_id' => $message['read']['mid'],
            ],
            'timestamp' => gmdate('Y-m-d\TH:i:s\Z', $message['timestamp'] / 1000),
            'is_admin'  => false,
        ];
    }
}
