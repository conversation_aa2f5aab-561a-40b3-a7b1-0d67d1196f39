<?php

namespace App\Services\CommentService;

use App\Models\SpecialUser;
use App\Contracts\CommentActionInterface;

class CommentService
{
    /**
     * An Interface array to allow the class CommentService to dynamically
     * manage the different types of comment handlers for example @class CommentDeleteService
     * or @class CommentReplyService etc.
     *
     * @var CommentActionInterface[]
     */
    protected array $actions;

    /**
     * CommentService constructor
     * @param array $actions
     */
    public function __construct(array $actions)
    {
        $this->actions = $actions;
    }

    public function handle(SpecialUser $user, array $data, string $actionType): mixed
    {
        if (!isset($this->actions[$actionType])) {
            throw new \InvalidArgumentException("Invalid comment action: {$actionType}");
        }

        return $this->actions[$actionType]->handle($user, $data);
    }
}
