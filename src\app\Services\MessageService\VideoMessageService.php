<?php

namespace App\Services\MessageService;

use App\Contracts\PayloadBuilderInterface;
use App\Repositories\Interfaces\SpecialUserRepositoryInterface as SpecialUserRepoInterface;
use App\Services\Abstract\AbstractMessageService;

class VideoMessageService extends AbstractMessageService
{
    protected string $attachmentType = 'video';

    public function __construct(
        PayloadBuilderInterface $payloadBuilder,
        SpecialUserRepoInterface  $specialUserRepo
    ) {
        parent::__construct(
            $payloadBuilder,
            $specialUserRepo
        );
    }

    /**
     * Gets the message content for a video message.
     *
     * @param array $messageData
     * @return array
     */
    protected function getMessageContent(array $messageData): array
    {
        return [
            'attachment' => [
                'type' => $this->attachmentType,
                'payload' => [
                    'url' => $messageData['video_url'],
                ],
            ],
        ];
    }
}
