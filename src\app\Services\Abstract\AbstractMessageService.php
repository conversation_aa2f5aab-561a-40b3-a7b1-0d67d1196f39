<?php

namespace App\Services\Abstract;

use App\Contracts\HttpClientInterface;
use App\Contracts\MessageSenderInterface;
use App\Contracts\PayloadBuilderInterface;
use App\Factories\Http\HttpClientFactory;
use App\Models\SpecialUser;
use App\Repositories\Interfaces\SpecialUserRepositoryInterface as SpecialUserRepoInterface;
use Exception;
use Illuminate\Http\Client\Response;

abstract class AbstractMessageService implements MessageSenderInterface
{
    protected string $attachmentType;
    protected PayloadBuilderInterface $payloadBuilder;
    protected SpecialUserRepoInterface  $specialUserRepo;
    protected HttpClientInterface  $httpClient;

    public function __construct(
        PayloadBuilderInterface $payloadBuilder,
        SpecialUserRepoInterface  $specialUserRepo,
    ) {
        $this->payloadBuilder = $payloadBuilder;
        $this->specialUserRepo =  $specialUserRepo;
        $this->httpClient = HttpClientFactory::forMessages();
    }

    /**
     * Sends a message using the Instagram API.
     *
     * @param SpecialUser $user
     * @param array $messageData
     * @return string|null The message_id returned by the Instagram API, or null
     * @throws Exception If the request fails
     */
    public function sendMessage(SpecialUser $user, array $messageData): ?string
    {
        $decoratedContent = $this->getMessageContent($messageData);
        $payload = $this->payloadBuilder->build($messageData, $decoratedContent);

        $response = $this->sendRequest($user, $payload);

        return $this->extractMessageId($response);
    }

    /**
     * Gets the message content based on the message type.
     *
     * @param array $messageData
     * @return array
     */
    abstract protected function getMessageContent(array $messageData): array;

    /**
     * Sends the HTTP POST request to the Instagram API.
     *
     * @param SpecialUser $user
     * @param array $payload
     * @return Response
     */
    protected function sendRequest(SpecialUser $user, array $payload): Response
    {
        $token = $this->specialUserRepo->decryptAccessToken($user->access_token);

        $headers = [
            'Authorization' => 'Bearer ' . $token,
            'Content-Type'  => 'application/json',
        ];

        return $this->httpClient->post('/me/messages', $headers, $payload);
    }

    /**
     * Extracts the message ID from the response.
     *
     * @param Response $response
     * @return string|null
     */
    protected function extractMessageId(Response $response): ?string
    {
        $responseBody = $response->json();
        return $responseBody['message_id'] ?? null;
    }
}
