<?php

namespace App\Services\TelegramService;

use App\Services\Http\TelegramHttpClient;

class TelegramMessageService
{
    /**
     * Send a message to a Telegram user.
     *
     * @param string $botToken The Telegram bot token
     * @param string $userId The Telegram user ID (chat_id)
     * @param string $message The message text to send
     * @return array The response from Telegram API
     * @throws \Exception If the request fails
     */
    public function sendMessage(string $botToken, string $userId, string $message): array
    {
        // Create a new HTTP client with the provided bot token
        $httpClient = new TelegramHttpClient($botToken);

        $payload = [
            'chat_id' => $userId,
            'text' => $message,
        ];

        $headers = [
            'Content-Type' => 'application/json',
        ];

        $response = $httpClient->post('/sendMessage', $headers, $payload);

        if (!$response->successful()) {
            throw new \Exception('Failed to send Telegram message: ' . $response->body());
        }

        return $response->json();
    }
}
