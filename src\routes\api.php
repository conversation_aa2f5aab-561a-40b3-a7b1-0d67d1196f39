<?php
/*
|--------------------------------------------------------------------------
| Webservice API Routes
|--------------------------------------------------------------------------
|
| This file defines the API routes for handling web service interactions.
| It includes routes for sending messages via the Messenger API and
| fetching user information from Instagram.
|
| All routes are protected by API exception handling middleware and
| rate-limiting to ensure stability and security.
|
*/

use App\Http\Controllers\Comment\CommentDeleteController;
use App\Http\Controllers\Comment\CommentHideController;
use App\Http\Controllers\Comment\CommentReplyController;
use App\Http\Controllers\Comment\CommentUnhideController;
use App\Http\Controllers\InstagramUser\InstagramFollowCountController;
use App\Http\Controllers\InstagramUser\InstagramFollowStatusController;
use App\Http\Controllers\InstagramUser\InstagramUsernameController;
use App\Http\Controllers\Messenger\AudioMessageController;
use App\Http\Controllers\Messenger\ButtonTextMessageController;
use App\Http\Controllers\Messenger\GenericTemplateMessageController;
use App\Http\Controllers\Messenger\PhotoMessageController;
use App\Http\Controllers\Messenger\QuickReplyMessageController;
use App\Http\Controllers\Messenger\VideoMessageController;
use App\Http\Controllers\Messenger\TextMessageController;
use App\Http\Controllers\Webhook\WebhookController;
use Illuminate\Support\Facades\Route;

// Health Checker APIs
require base_path('routes/health.php');

/*
|--------------------------------------------------------------------------
| Meta Webhook Route
|--------------------------------------------------------------------------
|
| This route handles incoming webhooks from Instagram.
| It processes, decorates, and transforms the webhook data into a structured
| and readable JSON format that informs our system or users about events such
| as new comments, direct messages, reactions, and more.
|
*/
Route::post('/instagram-webhook', [WebhookController::class, 'handle'])
    ->withoutMiddleware([
        'throttle:webservice_throttle',
    ]);

/*
|--------------------------------------------------------------------------
| Messenger API Routes
|--------------------------------------------------------------------------
|
| These routes handle the sending of different types of messages through
| the Messenger API. Supported message types include text, media,
| buttons, quick replies, and generic templates.
|
| Requests must be authenticated with a valid API token, and rate limits
| are enforced to prevent abuse.
|
*/
Route::prefix('send')->group(function () {
    Route::post('/text', [TextMessageController::class, 'send']);
    Route::post('/photo', [PhotoMessageController::class, 'send']);
    Route::post('/video', [VideoMessageController::class, 'send']);
    Route::post('/audio', [AudioMessageController::class, 'send']);
    Route::post('/button-text', [ButtonTextMessageController::class, 'send']);
    Route::post('/quick-reply', [QuickReplyMessageController::class, 'send']);
    Route::post('/generic-template', [GenericTemplateMessageController::class, 'send']);
});

/*
|--------------------------------------------------------------------------
| Comment API Routes
|--------------------------------------------------------------------------
|
| These routes manage comment interactions on Instagram posts.
| Supported actions include replying to a comment, hiding/unhiding,
| and deleting comments.
|
| All comment routes are protected by API authentication and validation.
|
*/
Route::prefix('comment')->group(function () {
    Route::post('/reply', [CommentReplyController::class, 'reply']);
    Route::post('/hide', [CommentHideController::class, 'hide']);
    Route::post('/unhide', [CommentUnhideController::class, 'unhide']);
    Route::post('/delete', [CommentDeleteController::class, 'delete']);
});

/*
|--------------------------------------------------------------------------
| Instagram User API Routes
|--------------------------------------------------------------------------
|
| These routes handle fetching specific user information from Instagram.
| Users can retrieve their username, follower count, and follow status.
|
| Requests must be authenticated with a valid API token, and rate limits
| are enforced to prevent abuse.
|
*/
Route::prefix('instagram-user')->group(function () {
    Route::post('/username', [InstagramUsernameController::class, 'fetchInfo']);
    Route::post('/follow-count', [InstagramFollowCountController::class, 'fetchInfo']);
    Route::post('/follow-status', [InstagramFollowStatusController::class, 'fetchInfo']);
});
