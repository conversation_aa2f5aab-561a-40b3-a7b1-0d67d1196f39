<?php

namespace App\Services\Token;

class TokenParser
{
    public function parse(string $encryptedToken): array
    {
        $data = json_decode($encryptedToken);

        if (!is_array($data) || count($data) !== 3) {
            throw new \InvalidArgumentException('Invalid encrypted token format');
        }

        return [
            base64_decode($data[0]),
            base64_decode($data[1]),
            base64_decode($data[2]),
        ];
    }
}
