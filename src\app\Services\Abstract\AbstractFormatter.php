<?php

namespace App\Services\Abstract;

use App\Contracts\ChainableFormatter;

abstract class AbstractFormatter implements ChainableFormatter
{
    protected ?ChainableFormatter $next = null;

    public function setNext(ChainableFormatter $formatter): ChainableFormatter
    {
        $this->next = $formatter;
        return $formatter;
    }

    public function handle(array $message): ?array
    {
        if ($this->canHandle($message)) {
            return $this->format($message);
        }

        return $this->next?->handle($message);
    }

    /**
     * Determine if this formatter can handle the message.
     */
    abstract protected function canHandle(array $payload): bool;

    /**
     * Format the message. Implemented by concrete classes.
     */
    abstract public function format(array $message): array;
}
