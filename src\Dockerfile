# -------- Base Image --------
FROM dunglas/frankenphp:1.1.3-php8.3

# -------- Set Working Directory --------
WORKDIR /app

# -------- System Requirements --------
RUN apt-get update && apt-get install -y \
    unzip \
    git \
    curl \
    libpq-dev \
    libzip-dev \
    && docker-php-ext-install pdo pdo_pgsql zip pcntl

# -------- Install Redis Extension --------
RUN apt-get install -y libssl-dev && \
    pecl install redis && \
    docker-php-ext-enable redis

# -------- Composer Setup --------
COPY --from=composer:2 /usr/bin/composer /usr/bin/composer

# -------- Copy <PERSON>vel Files --------
COPY . .

# -------- Install Dependencies --------
RUN composer install --no-dev --optimize-autoloader \
    && php artisan config:clear \
    && php artisan route:clear \
    && php artisan view:clear

# -------- Expose Port --------
EXPOSE 8082

# -------- Start Octane with <PERSON>enPHP --------
CMD ["php", "artisan", "octane:start", "--server=frankenphp", "--host=0.0.0.0", "--port=8082"]
