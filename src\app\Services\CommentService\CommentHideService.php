<?php

namespace App\Services\CommentService;

use App\Repositories\Interfaces\SpecialUserRepositoryInterface as SpecialUserRepoInterface;
use App\Services\Abstract\AbstractCommentService;

class CommentHideService extends AbstractCommentService
{
    public function __construct(
        SpecialUserRepoInterface  $specialUserRepo
    ) {
        parent::__construct($specialUserRepo);
    }

    public function getUrl(array $data): string
    {
        return "/{$data['comment_id']}";
    }

    public function getPayload(array $data): array
    {
        return ['hide' => true];
    }
}
