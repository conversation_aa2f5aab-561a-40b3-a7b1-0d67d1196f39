<?php

namespace App\Services\Abstract;

use App\Contracts\HttpClientInterface;
use App\Contracts\InstagramUserInfoFetcherInterface;
use App\Factories\Http\HttpClientFactory;
use App\Models\SpecialUser;
use Illuminate\Http\Client\Response;

abstract class AbstractInstagramUserInfoService implements InstagramUserInfoFetcherInterface
{
    protected string $fieldType;
    protected HttpClientInterface $httpClient;

    public function __construct()
    {
        $this->httpClient = HttpClientFactory::forUserInfo();
    }

    /**
     * Fetch user information from the Instagram API.
     *
     * @param SpecialUser $user
     * @param string $userId
     * @return array
     */
    public function fetchUserInfo(SpecialUser $user, string $userId): array
    {
        $query = [
            'fields'        => $this->fieldType,
            'access_token'  => $user->access_token,
        ];

        $response = $this->sendRequest("/{$userId}", $query);

        return $this->extractData($response);
    }

    /**
     * Sends the HTTP GET request to the Instagram API.
     *
     * @param string $url
     * @param array $query
     * @return Response
     */
    protected function sendRequest(string $url, array $query): Response
    {
        return $this->httpClient->get($url,$query);
    }

    /**
     * Extracts the requested data from the API response.
     *
     * @param Response $response
     * @return array
     */
    protected function extractData(Response $response): array
    {
        return $response->json() ?? [];
    }
}
