<?php

namespace App\Providers;

use App\Contracts\TokenDecryptorInterface;
use App\Services\CommentService\CommentDeleteService;
use App\Services\CommentService\CommentHideService;
use App\Services\CommentService\CommentReplyService;
use App\Services\CommentService\CommentService;
use App\Services\CommentService\CommentUnhideService;
use App\Services\InstagramUserService\FollowerCountFetcherService;
use App\Services\InstagramUserService\FollowerStatusFetcherService;
use App\Services\InstagramUserService\FollowingStatusFetcherService;
use App\Services\InstagramUserService\InstagramUserInfoService;
use App\Services\InstagramUserService\UsernameFetcherService;
use App\Services\MessageService\AudioMessageService;
use App\Services\MessageService\ButtonTextMessageService;
use App\Services\MessageService\GenericTemplateMessageService;
use App\Services\MessageService\ImageMessageService;
use App\Services\MessageService\MessageService;
use App\Services\MessageService\QuickReplyMessageService;
use App\Services\MessageService\TextMessageService;
use App\Services\MessageService\VideoMessageService;
use App\Services\Token\PhpseclibTokenDecryptor;
use App\Services\Token\TokenParser;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(MessageService::class, function ($app) {
            return new MessageService([
                'text'             => $app->make(TextMessageService::class),
                'video'            => $app->make(VideoMessageService::class),
                'image'            => $app->make(ImageMessageService::class),
                'audio'            => $app->make(AudioMessageService::class),
                'button-text'      => $app->make(ButtonTextMessageService::class),
                'quick-reply'      => $app->make(QuickReplyMessageService::class),
                'generic-template' => $app->make(GenericTemplateMessageService::class),
            ]);
        });

        $this->app->bind(InstagramUserInfoService::class, function ($app) {
            return new InstagramUserInfoService([
                'username'     => $app->make(UsernameFetcherService::class),
                'follow_count' => $app->make(FollowerCountFetcherService::class),
                'is_follower'  => $app->make(FollowerStatusFetcherService::class),
                'is_following' => $app->make(FollowingStatusFetcherService::class),
            ]);
        });

        $this->app->bind(CommentService::class, function ($app) {
            return new CommentService([
                'reply'   => $app->make(CommentReplyService::class),
                'hide'    => $app->make(CommentHideService::class),
                'unhide'  => $app->make(CommentUnhideService::class),
                'delete'  => $app->make(CommentDeleteService::class),
            ]);
        });

        $this->app->bind(TokenDecryptorInterface::class, function ($app) {
            return new PhpseclibTokenDecryptor(
                $app->make(TokenParser::class)
            );
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
