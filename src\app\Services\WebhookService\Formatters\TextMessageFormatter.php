<?php

namespace App\Services\WebhookService\Formatters;

use App\Services\Abstract\AbstractFormatter;

class TextMessageFormatter extends AbstractFormatter
{
    protected function canHandle(array $payload): bool
    {
        return isset($payload['message']['text']) &&
            !isset($payload['message']['quick_reply']) &&
            !isset($payload['message']['reply_to']['story']);
    }

    public function format(array $message): array
    {
        return [
            'sender' => $message['sender']['id'],
            'receiver' => $message['recipient']['id'],
            'message_type' => 'text',
            'text' => $message['message']['text'],
            'timestamp' => gmdate('Y-m-d\TH:i:s\Z', $message['timestamp'] / 1000),
            'is_admin' => isset($message['message']['is_echo']),
        ];
    }
}
