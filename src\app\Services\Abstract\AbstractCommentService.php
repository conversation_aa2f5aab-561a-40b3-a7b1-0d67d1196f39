<?php

namespace App\Services\Abstract;

use App\Contracts\CommentActionInterface;
use App\Models\SpecialUser;
use App\Repositories\Interfaces\SpecialUserRepositoryInterface;
use App\Contracts\HttpClientInterface;
use App\Factories\Http\HttpClientFactory;
use Illuminate\Http\Client\Response;

abstract class AbstractCommentService implements CommentActionInterface
{
    protected HttpClientInterface $httpClient;
    protected SpecialUserRepositoryInterface $specialUserRepo;

    public function __construct(SpecialUserRepositoryInterface $repo)
    {
        $this->specialUserRepo = $repo;
        $this->httpClient = HttpClientFactory::forComments();
    }

    public function handle(SpecialUser $user, array $data): ?string
    {
        $url = $this->getUrl($data);
        $payload = $this->getPayload($data);
        $headers = $this->getAuthHeaders($user);

        $response =  $this->dispatch($url, $headers, $payload);
        return $this->processResponse($response);
    }

    protected function getAuthHeaders(SpecialUser $user): array
    {
        $token = $this->specialUserRepo->decryptAccessToken($user->access_token);
        return ['Authorization' => 'Bearer ' . $token];
    }

    protected function dispatch(string $url, array $headers, array $payload): Response
    {
        return $this->httpClient->post($url, $headers, $payload);
    }

    protected function processResponse(Response $response): ?string
    {
        return null;
    }

    abstract protected function getUrl(array $data): string;
    abstract protected function getPayload(array $data): array;
}
