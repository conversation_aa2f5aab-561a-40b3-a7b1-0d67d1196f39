<?php

namespace App\Services\MessageService;

use App\Contracts\PayloadBuilderInterface;
use App\Repositories\Interfaces\SpecialUserRepositoryInterface as SpecialUserRepoInterface;
use App\Services\Abstract\AbstractMessageService;

class QuickReplyMessageService extends AbstractMessageService
{
	protected string $attachmentType = 'template';

    public function __construct(
        PayloadBuilderInterface $payloadBuilder,
        SpecialUserRepoInterface  $specialUserRepo
    ) {
        parent::__construct(
            $payloadBuilder,
            $specialUserRepo
        );
    }

	/**
	 * Gets the message content for a text message.
	 *
	 * @param array $messageData
	 * @return array
	 */
	protected function getMessageContent(array $messageData): array
	{
		$quickReplies = [];

		foreach ($messageData['quick_replies'] as $quickReply) {
			$quickReplies[] = [
				'content_type' => 'text',
				'title'        => $quickReply['title'],
				'payload'      => $quickReply['payload'],
			];
		}

		return [
			'text'          => $messageData['text'],
			'quick_replies' => $quickReplies,
		];
	}
}
