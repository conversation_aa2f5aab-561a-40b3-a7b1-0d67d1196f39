<?php

namespace App\Services\MessageService\Payload;

use App\Contracts\PayloadBuilderInterface;

class CommentToDirectPayloadBuilder implements PayloadBuilderInterface
{
    public function build(array $messageData, array $decoratedContent): array
    {
        return [
            'recipient' => [
                'comment_id' => $messageData['comment_id'],
            ],
            'message'   => $decoratedContent,
        ];
    }
}
