<?php

namespace App\Services\WebhookService\Formatters;

use App\Services\Abstract\AbstractFormatter;

class ReelsMessageFormatter extends AbstractFormatter
{
    protected function canHandle(array $payload): bool
    {
        return isset($payload['message']['attachments'][0]['type']) &&
            $payload['message']['attachments'][0]['type'] === 'ig_reel';
    }

    public function format(array $message): array
    {
        return (new MediaMessageFormatter())->format($message, 'reels');
    }
}
