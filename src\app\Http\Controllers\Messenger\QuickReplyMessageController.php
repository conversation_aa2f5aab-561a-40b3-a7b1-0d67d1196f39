<?php

namespace App\Http\Controllers\Messenger;

use App\Exceptions\InvalidApiTokenException;
use App\Exceptions\InvalidEndpointException;
use App\Exceptions\InvalidMessageTypeException;
use App\Http\Controllers\Abstract\AbstractMessageController;
use App\Services\MessageService\MessageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class QuickReplyMessageController extends AbstractMessageController
{
	protected MessageService $messageService;

	public function __construct(MessageService $messageService)
	{
		$this->messageService = $messageService;
	}

	/**
	 * Sends a text message.
	 *
	 * @param Request $request
	 * @return JsonResponse
	 * @throws InvalidMessageTypeException
	 * @throws InvalidEndpointException
	 * @throws InvalidApiTokenException
	 * @throws ValidationException
	 */
	public function send(Request $request): JsonResponse
	{
		$specialUser = $this->validateRequest($request);

		$validator = Validator::make($request->all(), [
            'receiver_id' => 'required_without:comment_id|string',
            'comment_id'  => 'required_without:receiver_id|string',
			'text'                    => 'required|string',
			'quick_replies'           => 'required|array',
			'quick_replies.*.title'   => 'required|string',
			'quick_replies.*.payload' => 'required|string',
		]);

		if ($validator->fails()) {
			return response()->json([
				'success' => false,
				'message' => 'Validation failed',
				'errors'  => $validator->errors(),
			], 422);
		}

		$messageId = $this->messageService->sendMessage(
			$specialUser,
			$validator->validated(),
			'quick-reply',
		);

		return response()->json([
			'success'    => true,
			'message'    => 'Message sent successfully',
			'message_id' => $messageId,
		]);
	}
}
