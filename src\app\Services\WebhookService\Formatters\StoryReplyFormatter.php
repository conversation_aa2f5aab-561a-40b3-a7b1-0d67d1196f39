<?php

namespace App\Services\WebhookService\Formatters;

use App\Services\Abstract\AbstractFormatter;

class StoryReplyFormatter extends AbstractFormatter
{
    protected function canHandle(array $payload): bool
    {
        return isset($payload['message']['reply_to']['story']);
    }

    public function format(array $message): array
    {
        return [
            'sender'       => $message['sender']['id'],
            'receiver'     => $message['recipient']['id'],
            'message_type' => 'story_reply',
            'reply_text'   => $message['message']['text'],
            'story_url'    => $message['message']['reply_to']['story']['url'],
            'story_id'     => $message['message']['reply_to']['story']['id'],
            'timestamp'    => gmdate('Y-m-d\TH:i:s\Z', $message['timestamp'] / 1000),
            'is_admin'     => isset($message['message']['is_echo']),
        ];
    }
}
