<?php

namespace App\Services\WebhookService\Formatters;

use App\Services\Abstract\AbstractFormatter;

class LiveCommentFormatter extends AbstractFormatter
{
    protected function canHandle(array $payload): bool
    {
        return isset($payload['changes'][0]['field']) &&
            $payload['changes'][0]['field'] === 'live_comments';
    }

    public function format(array $message): array
    {
        return [
            'sender'       => $message['changes'][0]['value']['from']['id'],
            'receiver'     => $message['id'],
            'message_type' => 'live_comment',
            'comment_text' => $message['changes'][0]['value']['text'],
            'comment_id'   => $message['changes'][0]['value']['id'],
            'live_id'      => $message['changes'][0]['value']['media']['id'],
            'timestamp'    => gmdate('Y-m-d\TH:i:s\Z', $message['time']),
            'is_admin'     => $message['changes'][0]['value']['from']['id'] === $message['id'],
        ];
    }
}
