<?php

namespace App\Traits;

use App\Exceptions\InvalidApiTokenException;
use App\Exceptions\InvalidEndpointException;
use App\Models\SpecialUser;
use App\Services\Validators\ApiTokenValidationService;
use App\Services\Validators\ValidEndpointValidationService;
use Illuminate\Http\Request;

trait ValidateRequest
{
    /**
     * Validates the API token and the request origin.
     *
     * @param Request $request
     * @return SpecialUser
     * @throws InvalidEndpointException
     * @throws InvalidApiTokenException
     */
    protected function validateRequest(Request $request): SpecialUser
    {
        $apiToken = $request->header('api-token');
        $specialUser = ApiTokenValidationService::validateApiToken($apiToken);

        $requestOrigin = $request->ip();
        ValidEndpointValidationService::validateEndpoint($specialUser, $requestOrigin);

        return $specialUser;
    }
}
