<?php

namespace App\Repositories\Interfaces;

use App\Models\SpecialUser;

interface SpecialUserRepositoryInterface
{
    public function findByInstagramId(string $instagramId): ?SpecialUser;

    public function existsByInstagramId(string $instagramId): bool;

    public function isValidEndpoint(SpecialUser $user, string $endpoint): bool;

    public function setApiToken(SpecialUser $user, string $apiToken): void;

    public function getUserByApiToken(string $apiToken): ?SpecialUser;

    public function decryptAccessToken(string $encryptedToken): string;
}
