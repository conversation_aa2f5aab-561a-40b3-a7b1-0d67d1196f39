<?php

namespace App\Services\WebhookService;

use App\Services\Abstract\AbstractFormatter;
use InvalidArgumentException;

class FormatterChainBuilder
{
    public function build(array $formatters): AbstractFormatter
    {
        if (empty($formatters)) {
            throw new InvalidArgumentException('No formatters provided');
        }

        for ($i = 0; $i < count($formatters) - 1; $i++) {
            $formatters[$i]->setNext($formatters[$i + 1]);
        }

        return $formatters[0];
    }
}
