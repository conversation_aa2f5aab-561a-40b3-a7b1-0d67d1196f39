<?php

namespace App\Repositories;

use App\Contracts\TokenDecryptorInterface;
use App\Models\SpecialUser;
use App\Repositories\Interfaces\SpecialUserRepositoryInterface;
use Illuminate\Support\Facades\Hash;

class SpecialUserRepository implements SpecialUserRepositoryInterface
{
    protected TokenDecryptorInterface $tokenDecryptor;

    public function __construct(
        TokenDecryptorInterface $tokenDecryptor,
    ) {
        $this->tokenDecryptor = $tokenDecryptor;
    }

    public function findByInstagramId(string $instagramId): ?SpecialUser
    {
        return SpecialUser::where('instagram_id', $instagramId)->first();
    }

    public function existsByInstagramId(string $instagramId): bool
    {
        return SpecialUser::where('instagram_id', $instagramId)->exists();
    }

    public function isValidEndpoint(SpecialUser $user, string $endpoint): bool
    {
        return in_array($endpoint, $user->valid_endpoints);
    }

    public function setApiToken(SpecialUser $user, string $apiToken): void
    {
        $user->api_token = bcrypt($apiToken);
        $user->save();
    }

    public function getUserByApiToken(string $apiToken): ?SpecialUser
    {
        return SpecialUser::whereNotNull('api_token')
            ->get()
            ->first(fn($user) => Hash::check($apiToken, $user->api_token));
    }

    public function decryptAccessToken(string $encryptedToken): string
    {
        return $this->tokenDecryptor->decrypt($encryptedToken);
    }
}
