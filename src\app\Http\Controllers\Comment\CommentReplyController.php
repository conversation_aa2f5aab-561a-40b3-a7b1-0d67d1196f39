<?php

namespace App\Http\Controllers\Comment;

use App\Exceptions\InvalidApiTokenException;
use App\Exceptions\InvalidEndpointException;
use App\Http\Controllers\Abstract\AbstractCommentController;
use App\Services\CommentService\CommentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class CommentReplyController extends AbstractCommentController
{
    protected CommentService $commentService;

    public function __construct(CommentService $commentService)
    {
        $this->commentService = $commentService;
    }

    /**
     * Replies a comment with another comment
     *
     * @param Request $request
     * @return JsonResponse
     * @throws InvalidEndpointException
     * @throws InvalidApiTokenException
     * @throws ValidationException
     */
    public function reply(Request $request): JsonResponse
    {
        $specialUser = $this->validateRequest($request);

        $validator = Validator::make($request->all(), [
            'comment_id' => 'required|string',
            'text'       => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors'  => $validator->errors(),
            ], 422);
        }

        $commentId = $this->commentService->handle(
            $specialUser,
            $validator->validated(),
            'reply'
        );

        return response()->json([
            'success'    => true,
            'message'    => 'Reply sent successfully',
            'comment_id' => $commentId,
        ], 200);
    }
}
