<?php

namespace App\Services\MessageService;

use App\Contracts\PayloadBuilderInterface;
use App\Repositories\Interfaces\SpecialUserRepositoryInterface as SpecialUserRepoInterface;
use App\Services\Abstract\AbstractMessageService;

class ButtonTextMessageService extends AbstractMessageService
{
	protected string $attachmentType = 'template';

    public function __construct(
        PayloadBuilderInterface $payloadBuilder,
        SpecialUserRepoInterface  $specialUserRepo
    ) {
        parent::__construct(
            $payloadBuilder,
            $specialUserRepo
        );
    }

	/**
	 * Gets the message content for a text message.
	 *
	 * @param array $messageData
	 * @return array
	 */
	protected function getMessageContent(array $messageData): array
	{
		return [
			'attachment' => [
				'type'    => $this->attachmentType,
				'payload' => [
					'template_type' => 'button',
					'text'          => $messageData['text'],
					'buttons'       => $messageData['buttons'],
				],
			],
		];
	}
}
