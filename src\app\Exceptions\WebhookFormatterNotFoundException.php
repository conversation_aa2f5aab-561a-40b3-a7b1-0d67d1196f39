<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WebhookFormatterNotFoundException extends Exception
{
    protected ?array $payload;

    /**
     * Create a new exception instance.
     *
     * @param array|null $payload The webhook payload that could not be handled
     * @param string|null $message
     */
    public function __construct(?array $payload = null, ?string $message = null)
    {
        $this->payload = $payload;

        $message = $message ?: 'No suitable formatter found for the provided webhook payload.';

        parent::__construct($message);
    }

    /**
     * Get the webhook payload related to this exception.
     *
     * @return array|null
     */
    public function getPayload(): ?array
    {
        return $this->payload;
    }

    /**
     * Report the exception to the logs or external services.
     *
     * @return void
     */
    public function report(): void
    {
        Log::error('Webhook formatter not found.', [
            'exception_message' => $this->getMessage(),
            'payload'           => $this->payload,
        ]);
    }

    /**
     * Render the exception into an HTTP JSON response.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function render(Request $request): JsonResponse
    {
        return response()->json([
            'error'   => $this->getMessage(),
            'payload' => $this->payload,
        ], 400);
    }
}
