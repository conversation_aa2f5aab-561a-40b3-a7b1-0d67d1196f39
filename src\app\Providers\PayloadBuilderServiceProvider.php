<?php

namespace App\Providers;

use App\Contracts\PayloadBuilderInterface;
use App\Services\MessageService\Payload\CommentToDirectPayloadBuilder;
use App\Services\MessageService\Payload\DirectToDirectPayloadBuilder;
use Illuminate\Support\ServiceProvider;

class PayloadBuilderServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(PayloadBuilderInterface::class, function ($app) {
            if (request()->has('comment_id')) {
                return new CommentToDirectPayloadBuilder();
            }

            return new DirectToDirectPayloadBuilder();
        });
    }

    public function boot()
    {
        // You can add any boot logic if needed
    }
}
