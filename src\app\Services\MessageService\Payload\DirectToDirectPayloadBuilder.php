<?php

namespace App\Services\MessageService\Payload;

use App\Contracts\PayloadBuilderInterface;

class DirectToDirectPayloadBuilder implements PayloadBuilderInterface
{
    public function build(array $messageData, array $decoratedContent): array
    {
        return [
            'recipient' => [
                'id' => $messageData['receiver_id'],
            ],
            'message'   => $decoratedContent,
        ];
    }
}
