<?php

namespace App\Contracts;

use App\Models\SpecialUser;

/**
 * Interface CommentActionInterface
 *
 * Defines the contract for executing comment-related actions
 * on behalf of a given `SpecialUser`, such as replying, hiding,
 * un-hiding, or deleting a comment on Instagram.
 *
 * Implementations of this interface encapsulate a single action type and must
 * handle authorization, payload preparation, and response parsing internally.
 *
 * Each implementation must adhere to the SOLID principles—particularly SRP and OCP—
 * to allow seamless extension of new comment action types.
 */
interface CommentActionInterface
{
    /**
     * Executes a comment action (e.g., reply, hide, unhide, delete) for a given user.
     *
     * The implementation should:
     * - Decrypt the user's access token
     * - Authorize the request with the appropriate headers
     * - Prepare the required payload based on the action
     * - Dispatch the HTTP request to the Instagram Graph API
     * - Optionally parse and return relevant data from the response
     *
     * @param SpecialUser $user The special user performing the comment action (access token required).
     * @param array $data The request-specific data such as `comment_id`, `message`, etc.
     * @return string|null Optional result like `message_id` for replies, or `null` for actions like hide/delete.
     *
     * @example **Reply to a comment**
     * ```php
     * $data = ['comment_id' => '123', 'message' => 'Thanks for your feedback!'];
     * $replyService->handle($user, $data);
     * // Returns: '456' (new reply comment_id)
     * ```
     *
     * @example **Delete a comment**
     * ```php
     * $data = ['comment_id' => '123'];
     * $deleteService->handle($user, $data);
     * // Returns: null
     * ```
     *
     * @example **Hide a comment**
     * ```php
     * $data = ['comment_id' => '123'];
     * $hideService->handle($user, $data);
     * // Returns: null
     * ```
     *
     * @example **Unhide a comment**
     * ```php
     * $data = ['comment_id' => '123'];
     * $unhideService->handle($user, $data);
     * // Returns: null
     * ```
     */
    public function handle(SpecialUser $user, array $data): ?string;
}
