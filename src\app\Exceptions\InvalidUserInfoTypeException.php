<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class InvalidUserInfoTypeException extends Exception
{
    protected string $field;

    /**
     * Creates a new exception instance.
     *
     * @param string $field
     * @param string|null $message
     */
    public function __construct(string $field, $message = null)
    {
        $this->field = $field;

        $message = $message ?: "User info field '{$field}' is not supported.";

        parent::__construct($message);
    }

    /**
     * Gets the requested user info field.
     *
     * @return string|null
     */
    public function getField(): ?string
    {
        return $this->field;
    }

    /**
     * Reports the exception to the logs or external services.
     *
     * @return void
     */
    public function report(): void
    {
        Log::error("Invalid user info field: {$this->field}", ['exception' => $this->getMessage()]);
    }

    /**
     * Renders the exception into an HTTP response.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function render(Request $request): JsonResponse
    {
        return response()->json([
            'error' => $this->getMessage(),
            'field' => $this->getField(),
        ], status: 400);
    }
}
