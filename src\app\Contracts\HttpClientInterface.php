<?php

namespace App\Contracts;

use Illuminate\Http\Client\Response;

/**
 * Interface HttpClientInterface
 *
 * This interface abstracts the underlying HTTP client implementation,
 * enabling retry logic, logging, or other cross-cutting concerns via decorators
 * while adhering to the Dependency Inversion Principle (DIP).
 */
interface HttpClientInterface
{
    /**
     * Sends a GET request to the specified path with optional headers and query parameters.
     *
     * @param string $path The relative URL path (appended to base URL).
     * @param array $headers Optional HTTP headers to include in the request.
     * @param array $query Optional query parameters to include in the URL.
     * @return Response The HTTP response returned by the server.
     *
     * @example **GET request with headers and query params**
     * ```php
     * $response = $httpClient->get('/posts', [
     *     'Authorization' => 'Bearer TOKEN',
     * ], [
     *     'limit' => 10,
     *     'offset' => 0,
     * ]);
     * ```
     */
    public function get(string $path, array $headers = [], array $query = []): Response;

    /**
     * Sends a POST request to the specified path with headers and an optional body.
     *
     * @param string $path The relative URL path (appended to base URL).
     * @param array $headers HTTP headers to include in the request.
     * @param array $body Optional associative array to be sent as the JSON body.
     * @return Response The HTTP response returned by the server.
     *
     * @example **POST request with JSON body**
     * ```php
     * $response = $httpClient->post('/comments', [
     *     'Authorization' => 'Bearer TOKEN',
     *     'Content-Type' => 'application/json',
     * ], [
     *     'message' => 'Hello, world!',
     *     'post_id' => 123,
     * ]);
     * ```
     */
    public function post(string $path, array $headers, array $body): Response;

    /**
     * Sends a DELETE request to the specified path with optional headers and query parameters.
     *
     * @param string $path The relative URL path (appended to base URL).
     * @param array $headers Optional HTTP headers to include in the request.
     * @param array $query Optional query parameters to include in the URL.
     * @return Response The HTTP response returned by the server.
     *
     * @example **DELETE request with headers and query params**
     * ```php
     * $response = $httpClient->delete('/comments/123', [
     *     'Authorization' => 'Bearer TOKEN',
     * ]);
     * ```
     */
    public function delete(string $path, array $headers = [], array $query = []): Response;
}
