<?php

namespace App\Services\WebhookService;

use App\Models\SpecialUser;
use Illuminate\Http\Client\Pool;
use Illuminate\Support\Facades\Http;
use GuzzleHttp\Exception\RequestException;

class WebhookSender
{
    /**
     * Send a webhook to all valid endpoints of a SpecialUser asynchronously.
     *
     * This method retrieves the list of webhook endpoints associated with the user,
     * then sends the provided webhook data to each endpoint without waiting for each
     * response, allowing the requests to be sent simultaneously.
     *
     * @param SpecialUser $specialUser The user whose webhook endpoints we need to send data to.
     * @param array $webhookData The data we need to send in the webhook.
     * @return void
     * @throws RequestException
     */
    public function sendWebhook(SpecialUser $specialUser, array $webhookData): void
    {
        $endpoints = $specialUser->webhook_endpoints;

        Http::pool(function (Pool $pool) use ($webhookData, $endpoints) {
            return collect()
                ->range(0, count($endpoints)-1)
                ->map(fn ($endpoint) => $pool->post($endpoints[$endpoint], $webhookData));
        });
    }
}
