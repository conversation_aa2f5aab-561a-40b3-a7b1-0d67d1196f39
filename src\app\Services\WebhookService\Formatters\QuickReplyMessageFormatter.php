<?php

namespace App\Services\WebhookService\Formatters;

use App\Services\Abstract\AbstractFormatter;

class QuickReplyMessageFormatter extends AbstractFormatter
{
    protected function canHandle(array $payload): bool
    {
        return isset($payload['message']['quick_reply']);
    }

    public function format(array $message): array
	{
		return [
			'sender'       => $message['sender']['id'],
			'receiver'     => $message['recipient']['id'],
			'message_type' => 'quick_reply',
			'text'         => $message['message']['text'],
			'payload'      => $message['message']['quick_reply']['payload'],
			'timestamp'    => gmdate('Y-m-d\TH:i:s\Z', $message['timestamp'] / 1000),
			'is_admin'     => isset($message['message']['is_echo']),
		];
	}
}
