<?php

use App\Http\Middleware\CloudflareProxies as RealIp;
use App\Http\Middleware\HandleApiExceptions;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        apiPrefix:'',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->trustProxies(at: '*');
        $middleware->append(RealIp::class);
        $middleware->append(HandleApiExceptions::class);
        $middleware->group('api', [
            'throttle:webservice_throttle',
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
