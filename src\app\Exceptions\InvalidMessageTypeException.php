<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class InvalidMessageTypeException extends Exception
{
    protected string $messageType;

    /**
     * Creates a new exception instance.
     *
     * @param string $messageType
     * @param string|null $message
     */
    public function __construct(string $messageType, $message = null)
    {
        $this->messageType = $messageType;

        $message = $message ?: "Message type '{$messageType}' is not supported.";

        parent::__construct($message);
    }

    /**
     * Gets the message type.
     *
     * @return string|null
     */
    public function getMessageType(): ?string
    {
        return $this->messageType;
    }

    /**
     * Reports the exception to the logs or external services.
     *
     * @return void
     */
    public function report(): void
    {
        Log::error("Invalid message type: {$this->messageType}", ['exception' => $this->getMessage()]);
    }

    /**
     * Render the exception into an HTTP response.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function render(Request $request): JsonResponse
    {
        return response()->json([
            'error'        => $this->getMessage(),
            'message_type' => $this->getMessageType(),
        ], status: 400);
    }
}
