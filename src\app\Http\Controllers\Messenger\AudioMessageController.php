<?php

namespace App\Http\Controllers\Messenger;

use App\Exceptions\InvalidApiTokenException;
use App\Exceptions\InvalidEndpointException;
use App\Exceptions\InvalidMessageTypeException;
use App\Http\Controllers\Abstract\AbstractMessageController;
use App\Services\MessageService\MessageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class AudioMessageController extends AbstractMessageController
{
    protected MessageService $messageService;

    public function __construct(MessageService $messageService)
    {
        $this->messageService = $messageService;
    }

    /**
     * Sends an audio message.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws InvalidMessageTypeException
     * @throws InvalidEndpointException
     * @throws InvalidApiTokenException
     * @throws ValidationException
     */
    public function send(Request $request): JsonResponse
    {
        $specialUser = $this->validateRequest($request);

        $validator = Validator::make($request->all(), [
            'receiver_id' => 'required_without:comment_id|string',
            'comment_id'  => 'required_without:receiver_id|string',
            'audio_url'   => 'required|url',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors'  => $validator->errors(),
            ], 422);
        }

        $messageId = $this->messageService->sendMessage(
            $specialUser,
            $validator->validated(),
            'audio'
        );

        return response()->json([
            'success'    => true,
            'message'    => 'Audio sent successfully',
            'message_id' => $messageId
        ], status: 200);
    }
}
