<?php

namespace App\Services\CommentService;

use App\Repositories\Interfaces\SpecialUserRepositoryInterface as SpecialUserRepoInterface;
use App\Services\Abstract\AbstractCommentService;
use Illuminate\Http\Client\Response;

class CommentReplyService extends AbstractCommentService
{
    public function __construct(
        SpecialUserRepoInterface  $specialUserRepo
    ) {
        parent::__construct($specialUserRepo);
    }

    public function getUrl(array $data): string
    {
        return "/{$data['comment_id']}/replies";
    }

    public function getPayload(array $data): array
    {
        return ['message' => $data['text']];
    }

    protected function processResponse(Response $response): ?string
    {
        $responseBody = $response->json();
        return $responseBody['id'] ?? null;
    }
}
