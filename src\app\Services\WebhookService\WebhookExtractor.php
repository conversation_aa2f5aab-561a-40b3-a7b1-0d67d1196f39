<?php

namespace App\Services\WebhookService;

use App\Exceptions\WebhookFormatterNotFoundException;
use App\Services\Abstract\AbstractFormatter;

class WebhookExtractor
{
    protected WebhookPreprocessor $preprocessor;
    protected AbstractFormatter $firstFormatter;

    public function __construct(
        WebhookPreprocessor $preprocessor,
        AbstractFormatter $firstFormatter
    ) {
        $this->preprocessor = $preprocessor;
        $this->firstFormatter = $firstFormatter;
    }

    public function extract(array $webhookPayload): ?array
    {
        $normalizedWebhook = $this->preprocessor->preprocess($webhookPayload);
        if (!$normalizedWebhook) return null;

        $result = $this->firstFormatter->handle($normalizedWebhook);

        if ($result === null) {
            throw new WebhookFormatterNotFoundException(
                $webhookPayload, 'No formatter could handle the webhook'
            );
        }

        return $result;
    }
}
