# Telegram Message Service

A minimal Telegram service for sending messages to users.

## Usage

```php
use App\Services\TelegramService\TelegramMessageService;

$telegramService = new TelegramMessageService();

// Send a message
$response = $telegramService->sendMessage(
    botToken: 'YOUR_BOT_TOKEN_HERE',
    userId: 'USER_CHAT_ID',
    message: 'Hello from your bot!'
);

// The response will contain the Telegram API response
echo "Message sent! Message ID: " . $response['result']['message_id'];
```

## Parameters

- **botToken**: Your Telegram bot token (get it from @BotFather)
- **userId**: The chat ID of the user you want to send the message to
- **message**: The text message to send

## Response

The method returns the full JSON response from the Telegram API as an associative array.

## Error Handling

The method throws an `\Exception` if the request fails. You should wrap calls in try-catch blocks:

```php
try {
    $response = $telegramService->sendMessage($botToken, $userId, $message);
    // Handle success
} catch (\Exception $e) {
    // Handle error
    echo "Error: " . $e->getMessage();
}
```
